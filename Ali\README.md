# تطبيق MINI DCA - الصندوق الاجتماعي للتنمية

## وصف المشروع
تطبيق ويب احترافي مصمم للصندوق الاجتماعي للتنمية في الجمهورية اليمنية. يوفر واجهة مستخدم حديثة وسهلة الاستخدام لإدارة المشاريع والعمليات المختلفة.

## المميزات الرئيسية

### 🎨 التصميم
- واجهة مستخدم احترافية وحديثة
- دعم كامل للغة العربية والاتجاه من اليمين إلى اليسار (RTL)
- تصميم متجاوب يعمل على جميع الأجهزة
- ألوان متدرجة وتأثيرات بصرية جذابة
- رسوم متحركة سلسة وتفاعلية

### 🔧 الوظائف
- **اعتماد العينات**: إدارة وموافقة العينات
- **استلام الأعمال**: تتبع واستلام المشاريع المكتملة
- **أدوات الضبط**: إعدادات وأدوات التحكم
- **إدارة المذكرة**: إنشاء وإدارة المذكرات الرسمية
- **تصريح العمل**: إصدار وإدارة تصاريح العمل
- **الاختبارات**: إجراء وتتبع الاختبارات المختلفة

### 📱 التفاعل
- نظام إشعارات متقدم
- نوافذ منبثقة احترافية
- تنقل سفلي سهل الاستخدام
- تأثيرات بصرية عند التفاعل
- دعم اختصارات لوحة المفاتيح

## التقنيات المستخدمة

### Frontend
- **HTML5**: هيكل الصفحة
- **CSS3**: التصميم والتنسيق
  - Flexbox & Grid Layout
  - CSS Animations & Transitions
  - Responsive Design
  - Custom Properties (CSS Variables)
- **JavaScript (ES6+)**: التفاعل والوظائف
  - Event Handling
  - DOM Manipulation
  - Async/Await
  - Modular Code Structure

### المكتبات الخارجية
- **Font Awesome 6.0**: الأيقونات
- **Google Fonts (Cairo)**: الخطوط العربية

## هيكل المشروع
```
Ali/
├── index.html          # الصفحة الرئيسية
├── styles.css          # ملف التصميم الرئيسي
├── script.js           # ملف JavaScript الرئيسي
└── README.md           # توثيق المشروع
```

## كيفية التشغيل

### 1. التشغيل المحلي
```bash
# فتح الملف مباشرة في المتصفح
open index.html
```

### 2. خادم محلي (مستحسن)
```bash
# باستخدام Python
python -m http.server 8000

# باستخدام Node.js
npx http-server

# باستخدام PHP
php -S localhost:8000
```

ثم افتح المتصفح على: `http://localhost:8000`

## المتطلبات
- متصفح ويب حديث يدعم HTML5 و CSS3 و ES6
- اتصال بالإنترنت لتحميل الخطوط والأيقونات

## التطوير المستقبلي

### المرحلة التالية
- [ ] إضافة قاعدة بيانات
- [ ] تطوير API خلفي
- [ ] نظام تسجيل الدخول
- [ ] تقارير مفصلة
- [ ] تصدير البيانات

### التحسينات المقترحة
- [ ] وضع الظلام (Dark Mode)
- [ ] دعم اللغات المتعددة
- [ ] إشعارات فورية (Push Notifications)
- [ ] تطبيق محمول (PWA)
- [ ] تكامل مع أنظمة خارجية

## الدعم والمساهمة
هذا المشروع مفتوح للتطوير والتحسين. يمكنك المساهمة من خلال:
- الإبلاغ عن الأخطاء
- اقتراح مميزات جديدة
- تحسين التصميم
- إضافة وظائف جديدة

## الترخيص
هذا المشروع مطور خصيصاً للصندوق الاجتماعي للتنمية - الجمهورية اليمنية

## معلومات التطوير
- **المطور**: Ali
- **تاريخ الإنشاء**: يونيو 2025
- **الإصدار**: 1.0.0
- **حالة المشروع**: قيد التطوير النشط

---
**ملاحظة**: هذا التطبيق في مرحلة التطوير الأولى ويحتاج إلى المزيد من التطوير لإضافة الوظائف الكاملة.
