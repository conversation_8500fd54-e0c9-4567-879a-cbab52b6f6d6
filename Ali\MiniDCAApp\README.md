# تطبيق MINI DCA للأندرويد
## الصندوق الاجتماعي للتنمية - الجمهورية اليمنية

### 📱 وصف التطبيق
تطبيق أندرويد احترافي مصمم خصيصاً للصندوق الاجتماعي للتنمية في الجمهورية اليمنية. يوفر واجهة مستخدم حديثة وسهلة الاستخدام لإدارة المشاريع والعمليات المختلفة.

### ✨ المميزات الرئيسية

#### 🎨 التصميم
- واجهة مستخدم احترافية مطابقة للتصميم المطلوب
- دعم كامل للغة العربية والاتجاه من اليمين إلى اليسار (RTL)
- تصميم متجاوب Material Design
- ألوان متدرجة وتأثيرات بصرية جذابة
- رسوم متحركة سلسة وتفاعلية

#### 🔧 الوظائف المطورة
- **اعتماد العينات**: نظام متدرج للقوائم
  - عينات المواد (خرسانة، حديد، أسمنت، ركام)
  - عينات تنفيذية الأعمال (أساسات، إنشائية، تشطيب، كهربائية)
- **استلام الأعمال**: (قيد التطوير)
- **أدوات الضبط**: (قيد التطوير)
- **إدارة المذكرة**: (قيد التطوير)
- **تصريح العمل**: (قيد التطوير)
- **الاختبارات**: (قيد التطوير)

#### 📱 التفاعل
- نظام إشعارات متقدم
- تأثيرات بصرية عند النقر والتمرير
- تنقل سلس بين الصفحات
- رسوم متحركة احترافية

### 🛠️ التقنيات المستخدمة

- **اللغة**: Kotlin
- **منصة**: Android (API 24+)
- **UI Framework**: Android Views + Material Design
- **Architecture**: Activity-based
- **Build System**: Gradle

### 📁 هيكل المشروع

```
MiniDCAApp/
├── app/
│   ├── build.gradle
│   ├── src/main/
│   │   ├── AndroidManifest.xml
│   │   ├── java/com/sfd/minidca/
│   │   │   ├── MainActivity.kt
│   │   │   ├── SampleApprovalActivity.kt
│   │   │   ├── MaterialSamplesActivity.kt
│   │   │   └── ExecutionSamplesActivity.kt
│   │   └── res/
│   │       ├── layout/
│   │       ├── values/
│   │       └── drawable/
└── build.gradle
```

### 🚀 كيفية التشغيل

#### المتطلبات
- Android Studio Arctic Fox أو أحدث
- Android SDK API 24 أو أحدث
- جهاز أندرويد أو محاكي

#### خطوات التشغيل
1. **فتح المشروع**:
   ```bash
   # افتح Android Studio
   # اختر "Open an existing project"
   # حدد مجلد MiniDCAApp
   ```

2. **بناء المشروع**:
   ```bash
   # في Android Studio
   Build > Make Project
   # أو اضغط Ctrl+F9
   ```

3. **تشغيل التطبيق**:
   ```bash
   # وصل جهاز أندرويد أو شغل محاكي
   # اضغط Run > Run 'app'
   # أو اضغط Shift+F10
   ```

#### تشغيل من سطر الأوامر
```bash
cd MiniDCAApp
./gradlew assembleDebug
./gradlew installDebug
```

### 📱 كيفية الاستخدام

1. **الصفحة الرئيسية**:
   - عرض معلومات المنظمة
   - قائمة الوظائف الرئيسية
   - آخر العمليات
   - نظام الإشعارات

2. **اعتماد العينات**:
   - اختر نوع العينات (مواد أو تنفيذية)
   - حدد العينة المطلوبة
   - تابع عملية الاعتماد

3. **التنقل**:
   - استخدم أزرار الرجوع للعودة
   - اضغط على الإشعارات لعرضها
   - تفاعل مع البطاقات بالنقر

### 🔄 التطوير المستقبلي

#### المرحلة التالية
- [ ] إكمال باقي الوظائف
- [ ] إضافة قاعدة بيانات محلية
- [ ] نظام تسجيل الدخول
- [ ] تقارير مفصلة
- [ ] تصدير البيانات

#### التحسينات المقترحة
- [ ] وضع الظلام (Dark Mode)
- [ ] إشعارات فورية (Push Notifications)
- [ ] تزامن البيانات مع الخادم
- [ ] دعم الأجهزة اللوحية
- [ ] تكامل مع أنظمة خارجية

### 🐛 استكشاف الأخطاء

#### مشاكل شائعة
1. **خطأ في البناء**: تأكد من تحديث Android Studio و SDK
2. **مشكلة في RTL**: تأكد من إعدادات اللغة في الجهاز
3. **بطء في التشغيل**: استخدم جهاز حقيقي بدلاً من المحاكي

#### الحلول
```bash
# تنظيف المشروع
./gradlew clean

# إعادة بناء المشروع
./gradlew build

# تحديث التبعيات
./gradlew --refresh-dependencies
```

### 📞 الدعم والمساهمة

هذا المشروع مطور خصيصاً للصندوق الاجتماعي للتنمية. للدعم التقني أو المساهمة:

- الإبلاغ عن الأخطاء
- اقتراح مميزات جديدة
- تحسين التصميم
- إضافة وظائف جديدة

### 📄 الترخيص

هذا المشروع مطور خصيصاً للصندوق الاجتماعي للتنمية - الجمهورية اليمنية

### 👨‍💻 معلومات التطوير

- **المطور**: Ali
- **تاريخ الإنشاء**: يونيو 2025
- **الإصدار**: 1.0.0
- **حالة المشروع**: قيد التطوير النشط

---

**ملاحظة مهمة**: هذا التطبيق في مرحلة التطوير الأولى. لتشغيله بشكل كامل، تحتاج إلى Android Studio وجهاز أندرويد أو محاكي.
