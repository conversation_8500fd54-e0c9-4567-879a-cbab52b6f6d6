// تطبيق MINI DCA - الصندوق الاجتماعي للتنمية
// تطوير: Ali 2025

document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    setupMenuItems();
    setupNotifications();
    setupBottomNavigation();
    addLoadingAnimations();
    console.log('تم تحميل التطبيق بنجاح');
}

// إعداد عناصر القائمة الرئيسية
function setupMenuItems() {
    const menuItems = document.querySelectorAll('.menu-item');
    
    menuItems.forEach(item => {
        item.addEventListener('click', function() {
            const action = this.getAttribute('data-action');
            handleMenuAction(action);
            
            // إضافة تأثير النقر
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
        
        // إضافة تأثير الحوم
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });
}

// معالجة إجراءات القائمة
function handleMenuAction(action) {
    const actions = {
        'sample-approval': () => openSampleApprovalPage(),
        'receive-work': () => showPage('استلام الأعمال', 'سيتم فتح صفحة استلام الأعمال'),
        'tools-control': () => showPage('أدوات الضبط', 'سيتم فتح صفحة أدوات الضبط'),
        'order-management': () => showPage('إدارة المذكرة', 'سيتم فتح صفحة إدارة المذكرة'),
        'work-permit': () => showPage('تصريح العمل', 'سيتم فتح صفحة تصريح العمل'),
        'tests': () => showPage('الاختبارات', 'سيتم فتح صفحة الاختبارات')
    };

    if (actions[action]) {
        actions[action]();
    }
}

// عرض صفحة جديدة
function showPage(title, message) {
    // إنشاء نافذة منبثقة احترافية
    const modal = document.createElement('div');
    modal.className = 'custom-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <p>${message}</p>
                <div class="modal-actions">
                    <button class="btn-primary">متابعة</button>
                    <button class="btn-secondary close-modal">إلغاء</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // إضافة الأنماط للنافذة المنبثقة
    addModalStyles();
    
    // إعداد إغلاق النافذة
    const closeButtons = modal.querySelectorAll('.close-modal');
    closeButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            modal.remove();
        });
    });
    
    // إغلاق عند النقر خارج النافذة
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// إعداد الإشعارات
function setupNotifications() {
    const notificationIcon = document.querySelector('.notification-icon');
    
    notificationIcon.addEventListener('click', function() {
        showNotifications();
    });
}

// عرض الإشعارات
function showNotifications() {
    const notifications = [
        {
            title: 'إشعار جديد',
            message: 'تم اعتماد عينة مشروع ترميم المدارس',
            time: 'منذ 5 دقائق',
            type: 'success'
        },
        {
            title: 'تنبيه',
            message: 'يرجى مراجعة تصريح العمل رقم 2025-001',
            time: 'منذ ساعة',
            type: 'warning'
        },
        {
            title: 'معلومة',
            message: 'تم تحديث نظام إدارة المشاريع',
            time: 'منذ يومين',
            type: 'info'
        }
    ];
    
    const modal = document.createElement('div');
    modal.className = 'custom-modal';
    modal.innerHTML = `
        <div class="modal-content notifications-modal">
            <div class="modal-header">
                <h3>الإشعارات</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="notifications-list">
                    ${notifications.map(notif => `
                        <div class="notification-item ${notif.type}">
                            <div class="notification-content">
                                <h4>${notif.title}</h4>
                                <p>${notif.message}</p>
                                <span class="notification-time">${notif.time}</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    addModalStyles();
    
    const closeButtons = modal.querySelectorAll('.close-modal');
    closeButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            modal.remove();
        });
    });
}

// إعداد التنقل السفلي
function setupBottomNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach((item, index) => {
        item.addEventListener('click', function() {
            // إزالة الفئة النشطة من جميع العناصر
            navItems.forEach(nav => nav.classList.remove('active'));
            // إضافة الفئة النشطة للعنصر المحدد
            this.classList.add('active');
            
            // معالجة التنقل
            handleNavigation(index);
        });
    });
}

// معالجة التنقل
function handleNavigation(index) {
    const pages = ['الرئيسية', 'القائمة', 'الرجوع'];
    console.log(`تم الانتقال إلى: ${pages[index]}`);
}

// إضافة الرسوم المتحركة للتحميل
function addLoadingAnimations() {
    const menuItems = document.querySelectorAll('.menu-item');
    
    menuItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// إضافة أنماط النافذة المنبثقة
function addModalStyles() {
    if (!document.getElementById('modal-styles')) {
        const style = document.createElement('style');
        style.id = 'modal-styles';
        style.textContent = `
            .custom-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            }
            
            .modal-content {
                background: white;
                border-radius: 15px;
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                animation: slideUp 0.3s ease;
            }
            
            .modal-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 15px 15px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .close-modal {
                cursor: pointer;
                font-size: 24px;
                font-weight: bold;
            }
            
            .modal-body {
                padding: 20px;
            }
            
            .modal-actions {
                display: flex;
                gap: 10px;
                margin-top: 20px;
                justify-content: flex-end;
            }
            
            .btn-primary, .btn-secondary {
                padding: 10px 20px;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-weight: 600;
                transition: all 0.3s ease;
            }
            
            .btn-primary {
                background: #3498db;
                color: white;
            }
            
            .btn-secondary {
                background: #95a5a6;
                color: white;
            }
            
            .btn-primary:hover {
                background: #2980b9;
            }
            
            .btn-secondary:hover {
                background: #7f8c8d;
            }
            
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            
            @keyframes slideUp {
                from { transform: translateY(30px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
            
            .notifications-list {
                max-height: 400px;
                overflow-y: auto;
            }
            
            .notification-item {
                padding: 15px;
                border-radius: 10px;
                margin-bottom: 10px;
                border-left: 4px solid;
            }
            
            .notification-item.success {
                background: #d4edda;
                border-color: #28a745;
            }
            
            .notification-item.warning {
                background: #fff3cd;
                border-color: #ffc107;
            }
            
            .notification-item.info {
                background: #d1ecf1;
                border-color: #17a2b8;
            }
            
            .notification-time {
                font-size: 12px;
                color: #6c757d;
            }
        `;
        document.head.appendChild(style);
    }
}

// إضافة مستمع للأحداث العامة
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.custom-modal');
        modals.forEach(modal => modal.remove());
    }
});

// صفحة اعتماد العينات
function openSampleApprovalPage() {
    const modal = document.createElement('div');
    modal.className = 'custom-modal sample-approval-modal';
    modal.innerHTML = `
        <div class="modal-content sample-approval-content">
            <div class="modal-header sample-approval-header">
                <h3>اعتماد العينات</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body sample-approval-body">
                <div class="submenu-grid">
                    <div class="submenu-item" data-option="materials">
                        <div class="submenu-icon">
                            <i class="fas fa-cubes"></i>
                        </div>
                        <h4>عينات المواد</h4>
                        <p>اعتماد وفحص عينات المواد الإنشائية</p>
                    </div>

                    <div class="submenu-item" data-option="execution">
                        <div class="submenu-icon">
                            <i class="fas fa-hard-hat"></i>
                        </div>
                        <h4>اعتماد عينات تنفيذية الأعمال</h4>
                        <p>اعتماد العينات التنفيذية للمشاريع</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    addSampleApprovalStyles();

    // إعداد إغلاق النافذة
    const closeButtons = modal.querySelectorAll('.close-modal');
    closeButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            modal.remove();
        });
    });

    // إعداد خيارات الاعتماد
    const optionCards = modal.querySelectorAll('.submenu-item');
    optionCards.forEach(card => {
        card.addEventListener('click', function() {
            const option = this.getAttribute('data-option');
            modal.remove(); // إغلاق النافذة الحالية
            handleApprovalOption(option);
        });
    });

    // إغلاق عند النقر خارج النافذة
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// معالجة خيارات الاعتماد
function handleApprovalOption(option) {
    if (option === 'materials') {
        openMaterialsSubmenu();
    } else if (option === 'execution') {
        openExecutionSubmenu();
    }
}

// قائمة فرعية لعينات المواد
function openMaterialsSubmenu() {
    const modal = document.createElement('div');
    modal.className = 'custom-modal submenu-modal';
    modal.innerHTML = `
        <div class="modal-content submenu-content">
            <div class="modal-header submenu-header">
                <h3>عينات المواد</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body submenu-body">
                <div class="submenu-grid">
                    <div class="submenu-item" data-action="concrete-samples">
                        <div class="submenu-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <h4>عينات الخرسانة</h4>
                        <p>فحص واعتماد عينات الخرسانة</p>
                    </div>

                    <div class="submenu-item" data-action="steel-samples">
                        <div class="submenu-icon">
                            <i class="fas fa-industry"></i>
                        </div>
                        <h4>عينات الحديد</h4>
                        <p>فحص واعتماد عينات الحديد</p>
                    </div>

                    <div class="submenu-item" data-action="cement-samples">
                        <div class="submenu-icon">
                            <i class="fas fa-hammer"></i>
                        </div>
                        <h4>عينات الأسمنت</h4>
                        <p>فحص واعتماد عينات الأسمنت</p>
                    </div>

                    <div class="submenu-item" data-action="aggregate-samples">
                        <div class="submenu-icon">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <h4>عينات الركام</h4>
                        <p>فحص واعتماد عينات الركام</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    setupSubmenuEvents(modal);
}

// قائمة فرعية لعينات تنفيذية الأعمال
function openExecutionSubmenu() {
    const modal = document.createElement('div');
    modal.className = 'custom-modal submenu-modal';
    modal.innerHTML = `
        <div class="modal-content submenu-content">
            <div class="modal-header submenu-header">
                <h3>اعتماد عينات تنفيذية الأعمال</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body submenu-body">
                <div class="submenu-grid">
                    <div class="submenu-item" data-action="foundation-work">
                        <div class="submenu-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <h4>أعمال الأساسات</h4>
                        <p>اعتماد عينات أعمال الأساسات</p>
                    </div>

                    <div class="submenu-item" data-action="structural-work">
                        <div class="submenu-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <h4>الأعمال الإنشائية</h4>
                        <p>اعتماد عينات الأعمال الإنشائية</p>
                    </div>

                    <div class="submenu-item" data-action="finishing-work">
                        <div class="submenu-icon">
                            <i class="fas fa-paint-brush"></i>
                        </div>
                        <h4>أعمال التشطيب</h4>
                        <p>اعتماد عينات أعمال التشطيب</p>
                    </div>

                    <div class="submenu-item" data-action="electrical-work">
                        <div class="submenu-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h4>الأعمال الكهربائية</h4>
                        <p>اعتماد عينات الأعمال الكهربائية</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    setupSubmenuEvents(modal);
}

// إعداد أحداث القائمة الفرعية
function setupSubmenuEvents(modal) {
    // إعداد إغلاق النافذة
    const closeButtons = modal.querySelectorAll('.close-modal');
    closeButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            modal.remove();
        });
    });

    // إعداد عناصر القائمة الفرعية
    const submenuItems = modal.querySelectorAll('.submenu-item');
    submenuItems.forEach(item => {
        item.addEventListener('click', function() {
            const action = this.getAttribute('data-action');
            const title = this.querySelector('h4').textContent;
            modal.remove();
            showActionPage(title, `سيتم فتح صفحة ${title}`);
        });
    });

    // إغلاق عند النقر خارج النافذة
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// عرض صفحة الإجراء
function showActionPage(title, message) {
    const modal = document.createElement('div');
    modal.className = 'custom-modal action-modal';
    modal.innerHTML = `
        <div class="modal-content action-content">
            <div class="modal-header action-header">
                <h3>${title}</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body action-body">
                <div class="action-info">
                    <div class="action-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <p>${message}</p>
                    <div class="action-buttons">
                        <button class="btn-primary">بدء العملية</button>
                        <button class="btn-secondary close-modal">إلغاء</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // إعداد إغلاق النافذة
    const closeButtons = modal.querySelectorAll('.close-modal');
    closeButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            modal.remove();
        });
    });

    // إغلاق عند النقر خارج النافذة
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// إضافة أنماط صفحة اعتماد العينات
function addSampleApprovalStyles() {
    if (!document.getElementById('sample-approval-styles')) {
        const style = document.createElement('style');
        style.id = 'sample-approval-styles';
        style.textContent = `
            .sample-approval-modal .modal-content,
            .submenu-modal .modal-content {
                max-width: 900px;
                width: 95%;
                max-height: 80vh;
            }

            .sample-approval-header,
            .submenu-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                text-align: center;
            }

            .sample-approval-body,
            .submenu-body {
                padding: 30px;
            }

            .submenu-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                max-width: 1000px;
                margin: 0 auto;
            }

            .submenu-item {
                background: white;
                border-radius: 15px;
                padding: 25px;
                text-align: center;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                cursor: pointer;
                transition: all 0.3s ease;
                border: 2px solid transparent;
                position: relative;
                overflow: hidden;
            }

            .submenu-item::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            .submenu-item:hover {
                transform: translateY(-8px);
                box-shadow: 0 12px 30px rgba(0,0,0,0.15);
                border-color: #667eea;
            }

            .submenu-icon {
                width: 60px;
                height: 60px;
                margin: 0 auto 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 28px;
                transition: all 0.3s ease;
            }

            .submenu-item:hover .submenu-icon {
                transform: scale(1.1);
                box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
            }

            .submenu-item h4 {
                font-size: 18px;
                font-weight: 600;
                color: #2c3e50;
                margin: 0 0 10px 0;
                line-height: 1.3;
            }

            .submenu-item p {
                font-size: 14px;
                color: #7f8c8d;
                margin: 0;
                line-height: 1.4;
            }

            .action-modal .modal-content {
                max-width: 500px;
                width: 90%;
            }

            .action-header {
                background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
                text-align: center;
            }

            .action-body {
                padding: 40px 30px;
                text-align: center;
            }

            .action-icon {
                width: 80px;
                height: 80px;
                margin: 0 auto 20px;
                background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 40px;
            }

            .action-info p {
                font-size: 16px;
                color: #2c3e50;
                margin-bottom: 30px;
                line-height: 1.5;
            }

            .action-buttons {
                display: flex;
                gap: 15px;
                justify-content: center;
            }

            @media (max-width: 768px) {
                .submenu-grid {
                    grid-template-columns: 1fr;
                    gap: 15px;
                }

                .submenu-body {
                    padding: 20px;
                }

                .submenu-item {
                    padding: 20px;
                }

                .submenu-icon {
                    width: 50px;
                    height: 50px;
                    font-size: 24px;
                }

                .submenu-item h4 {
                    font-size: 16px;
                }

                .action-buttons {
                    flex-direction: column;
                }
            }
        `;
        document.head.appendChild(style);
    }
}

console.log('تم تحميل ملف JavaScript بنجاح - تطبيق MINI DCA');
