package com.sfd.minidca

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat

class MainActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        setupUI()
        setupMenuItems()
    }
    
    private fun setupUI() {
        // إعداد شريط العنوان
        supportActionBar?.hide()
        
        // إعداد اتجاه RTL
        window.decorView.layoutDirection = android.view.View.LAYOUT_DIRECTION_RTL
    }
    
    private fun setupMenuItems() {
        // اعتماد العينات
        findViewById<CardView>(R.id.cardSampleApproval).setOnClickListener {
            animateCardClick(it as CardView) {
                startActivity(Intent(this, SampleApprovalActivity::class.java))
            }
        }
        
        // استلام الأعمال
        findViewById<CardView>(R.id.cardReceiveWork).setOnClickListener {
            animateCardClick(it as CardView) {
                showToast("سيتم فتح صفحة استلام الأعمال قريباً")
            }
        }
        
        // أدوات الضبط
        findViewById<CardView>(R.id.cardToolsControl).setOnClickListener {
            animateCardClick(it as CardView) {
                showToast("سيتم فتح صفحة أدوات الضبط قريباً")
            }
        }
        
        // إدارة المذكرة
        findViewById<CardView>(R.id.cardOrderManagement).setOnClickListener {
            animateCardClick(it as CardView) {
                showToast("سيتم فتح صفحة إدارة المذكرة قريباً")
            }
        }
        
        // تصريح العمل
        findViewById<CardView>(R.id.cardWorkPermit).setOnClickListener {
            animateCardClick(it as CardView) {
                showToast("سيتم فتح صفحة تصريح العمل قريباً")
            }
        }
        
        // الاختبارات
        findViewById<CardView>(R.id.cardTests).setOnClickListener {
            animateCardClick(it as CardView) {
                showToast("سيتم فتح صفحة الاختبارات قريباً")
            }
        }
        
        // الإشعارات
        findViewById<android.view.View>(R.id.notificationIcon).setOnClickListener {
            showNotifications()
        }
    }
    
    private fun animateCardClick(card: CardView, action: () -> Unit) {
        card.animate()
            .scaleX(0.95f)
            .scaleY(0.95f)
            .setDuration(100)
            .withEndAction {
                card.animate()
                    .scaleX(1.0f)
                    .scaleY(1.0f)
                    .setDuration(100)
                    .withEndAction {
                        action()
                    }
            }
    }
    
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    private fun showNotifications() {
        val notifications = arrayOf(
            "تم اعتماد عينة مشروع ترميم المدارس",
            "يرجى مراجعة تصريح العمل رقم 2025-001",
            "تم تحديث نظام إدارة المشاريع"
        )
        
        val builder = androidx.appcompat.app.AlertDialog.Builder(this)
        builder.setTitle("الإشعارات")
        builder.setItems(notifications) { dialog, which ->
            showToast("تم النقر على: ${notifications[which]}")
        }
        builder.setPositiveButton("إغلاق") { dialog, _ ->
            dialog.dismiss()
        }
        builder.show()
    }
}
