<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:layoutDirection="rtl"
    tools:context=".MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            android:background="@drawable/header_background"
            android:layout_marginBottom="16dp">

            <ImageView
                android:id="@+id/notificationIcon"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_notifications"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="4dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="الرئيسية"
                android:textSize="20sp"
                android:textColor="@android:color/white"
                android:textStyle="bold"
                android:gravity="center" />

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_arrow_back"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="4dp" />

        </LinearLayout>

        <!-- Organization Info -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="20dp"
                android:gravity="center_vertical">

                <LinearLayout
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:background="@drawable/logo_background"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="SFD"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الصندوق الاجتماعي"
                        android:textColor="@android:color/white"
                        android:textSize="8sp"
                        android:gravity="center" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الجمهورية اليمنية"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_dark" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="MINI DCA"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary"
                        android:layout_marginTop="4dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="رئاسة مجلس الوزراء\nالصندوق الاجتماعي للتنمية"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Special Banner -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="6dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="مساحة اعلانية خاصة بمسؤولي تقدم المشاريع"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:gravity="center"
                android:padding="16dp"
                android:background="@drawable/banner_background" />

        </androidx.cardview.widget.CardView>

        <!-- Main Menu Grid -->
        <GridLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:columnCount="3"
            android:rowCount="2"
            android:layout_marginBottom="20dp">

            <!-- اعتماد العينات -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardSampleApproval"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp"
                android:foreground="?attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_check_circle"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="اعتماد عينات"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_dark"
                        android:gravity="center" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- استلام الأعمال -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardReceiveWork"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp"
                android:foreground="?attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_clipboard"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="استلام أعمال"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_dark"
                        android:gravity="center" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- أدوات الضبط -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardToolsControl"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp"
                android:foreground="?attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_tools"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="أدوات ضبط"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_dark"
                        android:gravity="center" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- إدارة المذكرة -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardOrderManagement"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp"
                android:foreground="?attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_file"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="إدارة مذكرة"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_dark"
                        android:gravity="center" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- تصريح العمل -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardWorkPermit"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp"
                android:foreground="?attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_user_check"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="تصريح عمل"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_dark"
                        android:gravity="center" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- الاختبارات -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardTests"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp"
                android:foreground="?attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_vial"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="اختبارات"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_dark"
                        android:gravity="center" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </GridLayout>

        <!-- Latest Operations -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="80dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/operations_header_background"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="آخر العمليات"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/white" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_notifications"
                        android:tint="@android:color/white" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp"
                    android:background="#FAFBFC">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="اسم المشروع: مشروع ترميم مدارس صنعاء"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@color/primary_dark"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="نوع العملية: أذن صب"
                        android:textSize="14sp"
                        android:textColor="@color/primary"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الإنجاز: 85%"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="#27AE60"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="تاريخ آخر عملية: 20 يونيو 2025"
                        android:textSize="13sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
