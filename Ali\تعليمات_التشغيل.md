# 📱 تعليمات تشغيل تطبيق MINI DCA

## 🚀 طريقة سريعة للتشغيل

### الخطوة 1: تحميل Android Studio
1. اذهب إلى: https://developer.android.com/studio
2. حمل وثبت Android Studio
3. افتح Android Studio

### الخطوة 2: فتح المشروع
1. في Android Studio اختر **"Open an existing project"**
2. حدد مجلد **`MiniDCAApp`** من مجلد Ali
3. انتظر حتى يتم تحميل المشروع

### الخطوة 3: إعداد المحاكي (إذا لم يكن لديك جهاز أندرويد)
1. في Android Studio اذهب إلى **Tools > AVD Manager**
2. اضغط **"Create Virtual Device"**
3. اختر جهاز (مثل Pixel 4)
4. اختر نظام تشغيل (API 30 أو أحدث)
5. اضغط **Finish**

### الخطوة 4: تشغيل التطبيق
1. تأكد من أن المحاكي يعمل أو وصل جهاز أندرويد
2. اضغط على زر **"Run"** (المثلث الأخضر)
3. أو اضغط **Shift + F10**

## 📱 ما ستراه في التطبيق

### الصفحة الرئيسية
- **Header**: مع إشعارات وعنوان "الرئيسية"
- **معلومات المنظمة**: شعار SFD ومعلومات الصندوق
- **البانر الإعلاني**: مساحة إعلانية برتقالية
- **القائمة الرئيسية**: 6 بطاقات للوظائف المختلفة
- **آخر العمليات**: معلومات المشاريع الحديثة

### التفاعل مع التطبيق
1. **اضغط على "اعتماد عينات"** → ستفتح صفحة جديدة
2. **اختر "عينات المواد"** → ستظهر قائمة فرعية
3. **اختر أي عينة** → ستظهر رسالة تأكيد
4. **اضغط على الإشعارات** → ستظهر قائمة الإشعارات

## 🎯 المميزات المطورة

### ✅ يعمل حالياً:
- الصفحة الرئيسية كاملة
- صفحة اعتماد العينات
- القوائم الفرعية للعينات
- نظام الإشعارات
- التنقل بين الصفحات
- الرسوم المتحركة

### 🔄 قيد التطوير:
- باقي الوظائف (استلام أعمال، أدوات ضبط، إلخ)
- قاعدة البيانات
- نظام تسجيل الدخول

## 🛠️ حل المشاكل الشائعة

### مشكلة: التطبيق لا يعمل
**الحل**: تأكد من أن:
- Android Studio محدث
- SDK مثبت بشكل صحيح
- المحاكي يعمل أو الجهاز متصل

### مشكلة: النص لا يظهر بالعربية
**الحل**: 
- تأكد من أن لغة الجهاز/المحاكي عربية
- أو غير اللغة في إعدادات الجهاز

### مشكلة: بطء في التشغيل
**الحل**:
- استخدم جهاز أندرويد حقيقي بدلاً من المحاكي
- أو زود ذاكرة المحاكي

## 📞 للمساعدة

إذا واجهت أي مشكلة:
1. تأكد من اتباع الخطوات بالترتيب
2. راجع ملف README.md في مجلد MiniDCAApp
3. تأكد من أن جميع الملفات موجودة

## 🎉 استمتع بالتطبيق!

التطبيق مصمم ليكون سهل الاستخدام وتفاعلي. جرب جميع الوظائف واستكشف القوائم المختلفة!
