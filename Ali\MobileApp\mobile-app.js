// تطبيق MINI DCA المحمول
// تطوير: Ali 2025

class MobileApp {
    constructor() {
        this.currentScreen = 'mainScreen';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.addTouchEffects();
        console.log('تم تحميل التطبيق المحمول بنجاح');
    }

    setupEventListeners() {
        // Menu items
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const action = e.currentTarget.getAttribute('data-action');
                this.handleMenuAction(action);
            });
        });

        // Back buttons
        document.querySelectorAll('.back-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const backScreen = e.currentTarget.getAttribute('data-back');
                this.navigateToScreen(backScreen);
            });
        });

        // Approval cards
        document.querySelectorAll('.approval-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const action = e.currentTarget.getAttribute('data-action');
                this.handleApprovalAction(action);
            });
        });

        // Sample cards
        document.querySelectorAll('.sample-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const sample = e.currentTarget.getAttribute('data-sample');
                this.handleSampleAction(sample);
            });
        });

        // Material items
        document.querySelectorAll('.material-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const material = e.currentTarget.getAttribute('data-material');
                this.handleMaterialAction(material);
            });
        });

        // Spec items
        document.querySelectorAll('.spec-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const spec = e.currentTarget.getAttribute('data-spec');
                this.handleSpecAction(spec);
            });
        });

        // Execution items
        document.querySelectorAll('.execution-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const execution = e.currentTarget.getAttribute('data-execution');
                this.handleExecutionAction(execution);
            });
        });

        // Structural items
        document.querySelectorAll('.structural-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const structural = e.currentTarget.getAttribute('data-structural');
                this.handleStructuralAction(structural);
            });
        });

        // Notification button
        document.getElementById('notificationBtn').addEventListener('click', () => {
            this.showNotifications();
        });

        // Modal close
        document.querySelector('.close-modal').addEventListener('click', () => {
            this.hideModal();
        });

        // Modal background click
        document.getElementById('notificationModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.hideModal();
            }
        });

        // Bottom navigation
        document.querySelectorAll('.nav-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                this.handleNavigation(index);
            });
        });
    }

    addTouchEffects() {
        // Add haptic feedback simulation
        document.querySelectorAll('.menu-item, .approval-card, .sample-card, .nav-item').forEach(element => {
            element.addEventListener('touchstart', () => {
                // Simulate haptic feedback
                if (navigator.vibrate) {
                    navigator.vibrate(10);
                }
            });
        });
    }

    handleMenuAction(action) {
        const actions = {
            'sample-approval': () => this.navigateToScreen('sampleApprovalScreen'),
            'receive-work': () => this.showToast('سيتم فتح صفحة استلام الأعمال قريباً'),
            'tools-control': () => this.showToast('سيتم فتح صفحة أدوات الضبط قريباً'),
            'order-management': () => this.showToast('سيتم فتح صفحة إدارة المذكرة قريباً'),
            'work-permit': () => this.showToast('سيتم فتح صفحة تصريح العمل قريباً'),
            'tests': () => this.showToast('سيتم فتح صفحة الاختبارات قريباً')
        };

        if (actions[action]) {
            actions[action]();
        }
    }

    handleApprovalAction(action) {
        const actions = {
            'material-samples': () => this.navigateToScreen('materialSamplesScreen'),
            'execution-samples': () => this.navigateToScreen('executionSamplesScreen')
        };

        if (actions[action]) {
            actions[action]();
        }
    }

    handleSampleAction(sample) {
        const samples = {
            'concrete': () => this.navigateToScreen('concreteSamplesScreen'),
            'steel': () => this.showToast('سيتم فتح صفحة عينات الحديد قريباً'),
            'cement': () => this.showToast('سيتم فتح صفحة عينات الأسمنت قريباً'),
            'aggregate': () => this.showToast('سيتم فتح صفحة عينات الركام قريباً')
        };

        if (samples[sample]) {
            samples[sample]();
        }
    }

    handleMaterialAction(material) {
        const materials = {
            'mosaic-auto': 'بلاط موزايكو اتوماتيكي',
            'ceramic': 'بلاط سيراميك',
            'vitrified': 'بلاط فيتشا ني',
            'porcelain': 'بلاط بورسلان',
            'terrazzo': 'بلاط تعلات',
            'paint': 'مواد دهان',
            'paint2': 'مواد دهان (نوع ثاني)',
            'plaster': 'مواد سباكه',
            'electrical': 'مواد كهرباء'
        };

        this.showDetailedToast(`تم اختيار المادة`, materials[material], 'fas fa-cubes');
    }

    handleSpecAction(spec) {
        const specs = {
            'sand': 'رمل - مواد أساسية',
            'gravel': 'كري - ركام خشن',
            'cement': 'اسمنت - مادة رابطة',
            'iron': 'حديد - تسليح إنشائي',
            'crushed-stone': 'حجر كرسني - ركام متدرج',
            'brick-stone': 'حجر بطانة - مواد بناء',
            'facade-stone': 'حجر واجهات - تشطيب خارجي',
            'volume-stone': 'حجر حجم - أحجام مختلفة',
            'block-20': 'بلك صم 20 سم - بلوك إنشائي',
            'block-15': 'بلك صم 15 سم - بلوك متوسط',
            'block-10': 'بلك صم 10 سم - بلوك خفيف',
            'hollow-block': 'مفرغ 20 سم - بلوك مفرغ'
        };

        this.showDetailedToast(`تم اختيار المواصفة`, specs[spec], 'fas fa-ruler-combined');
    }

    handleExecutionAction(execution) {
        if (execution === 'reserved') {
            this.showToast('هذا القسم محجوز للتطوير المستقبلي');
            return;
        }

        const executions = {
            'mosaic-tiles': 'بلاط موزايكو - تشطيب أرضيات',
            'ceramic-tiles': 'بلاط سيراميك - تشطيب حديث',
            'vitrified-tiles': 'بلاط فيتشاني - تشطيب فاخر',
            'porcelain-tiles': 'بلاط بورسلان - تشطيب متقدم',
            'terrazzo-tiles': 'بلاط تعلات - تشطيب تقليدي',
            'sidewalk-tiles': 'بلاط ارصفه - أرصفة خارجية',
            'american-paint': 'رشه امريكي - دهانات متقدمة',
            'wall-paint': 'دهانات جدران - دهانات داخلية'
        };

        this.showDetailedToast(`تم اختيار مادة البناء`, executions[execution], 'fas fa-hammer');
    }

    handleStructuralAction(structural) {
        const structurals = {
            'stone-sample': 'عينة حجر - حجر طبيعي',
            'stone-building': 'مباني حجر حجم - بناء حجري',
            'iron-rules': 'حديد قواعد - تسليح أساسات',
            'iron-columns': 'حديد اعمده - تسليح أعمدة',
            'stone-crushed': 'مباني حجر كرسني - حجر مكسر',
            'stone-facades': 'مباني حجر واجهات - واجهات خارجية',
            'building-block': 'مباني بلك - بلوك إنشائي',
            'roof-insulation': 'تلابيس سقوف - عزل أسقف',
            'wall-insulation': 'تلابيس جدران - عزل جدران'
        };

        this.showDetailedToast(`تم اختيار العنصر الإنشائي`, structurals[structural], 'fas fa-building');
    }

    navigateToScreen(screenId) {
        // Hide current screen
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });

        // Show new screen with animation
        setTimeout(() => {
            document.getElementById(screenId).classList.add('active');
            this.currentScreen = screenId;
        }, 100);
    }

    showNotifications() {
        document.getElementById('notificationModal').classList.add('show');
    }

    hideModal() {
        document.getElementById('notificationModal').classList.remove('show');
    }

    handleNavigation(index) {
        // Remove active class from all nav items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to clicked item
        document.querySelectorAll('.nav-item')[index].classList.add('active');

        const pages = ['الرئيسية', 'القائمة', 'الرجوع'];
        
        if (index === 0) {
            this.navigateToScreen('mainScreen');
        } else if (index === 2) {
            this.goBack();
        }

        console.log(`تم الانتقال إلى: ${pages[index]}`);
    }

    goBack() {
        if (this.currentScreen === 'concreteSamplesScreen') {
            this.navigateToScreen('materialSamplesScreen');
        } else if (this.currentScreen === 'executionSamplesScreen') {
            this.navigateToScreen('sampleApprovalScreen');
        } else if (this.currentScreen === 'materialSamplesScreen') {
            this.navigateToScreen('sampleApprovalScreen');
        } else if (this.currentScreen === 'sampleApprovalScreen') {
            this.navigateToScreen('mainScreen');
        }
    }

    showToast(message) {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 14px;
            z-index: 2000;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        document.body.appendChild(toast);

        // Show toast
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 100);

        // Hide and remove toast
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 2000);
    }

    showDetailedToast(title, message, icon) {
        // Create detailed toast element
        const toast = document.createElement('div');
        toast.className = 'detailed-toast';
        toast.innerHTML = `
            <div class="toast-content">
                <div class="toast-icon">
                    <i class="${icon}"></i>
                </div>
                <div class="toast-text">
                    <h4>${title}</h4>
                    <p>${message}</p>
                </div>
            </div>
        `;
        toast.style.cssText = `
            position: fixed;
            bottom: 120px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 2000;
            opacity: 0;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            max-width: 300px;
            width: 90%;
        `;

        // Add styles for toast content
        const style = document.createElement('style');
        style.textContent = `
            .toast-content {
                display: flex;
                align-items: center;
                gap: 15px;
            }
            .toast-icon {
                width: 40px;
                height: 40px;
                background: rgba(255,255,255,0.2);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 18px;
                flex-shrink: 0;
            }
            .toast-text h4 {
                margin: 0 0 5px 0;
                font-size: 14px;
                font-weight: 600;
            }
            .toast-text p {
                margin: 0;
                font-size: 12px;
                opacity: 0.9;
                line-height: 1.3;
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(toast);

        // Show toast
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(-50%) translateY(-10px)';
        }, 100);

        // Hide and remove toast
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(-50%) translateY(10px)';
            setTimeout(() => {
                document.body.removeChild(toast);
                document.head.removeChild(style);
            }, 300);
        }, 3000);
    }

    // Simulate loading states
    showLoading() {
        const loader = document.createElement('div');
        loader.className = 'loader';
        loader.innerHTML = `
            <div style="
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 20px;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                z-index: 3000;
                text-align: center;
            ">
                <div style="
                    width: 40px;
                    height: 40px;
                    border: 4px solid #f3f3f3;
                    border-top: 4px solid #667eea;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 15px;
                "></div>
                <p style="color: #2c3e50; font-weight: 600;">جاري التحميل...</p>
            </div>
        `;

        // Add spinner animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(loader);

        // Remove loader after 1.5 seconds
        setTimeout(() => {
            document.body.removeChild(loader);
        }, 1500);
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MobileApp();
});

// Add some mobile-specific features
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        // Register service worker for PWA functionality
        console.log('PWA features available');
    });
}

// Prevent zoom on double tap
let lastTouchEnd = 0;
document.addEventListener('touchend', (event) => {
    const now = (new Date()).getTime();
    if (now - lastTouchEnd <= 300) {
        event.preventDefault();
    }
    lastTouchEnd = now;
}, false);

// Add pull-to-refresh simulation
let startY = 0;
let currentY = 0;
let pullDistance = 0;

document.addEventListener('touchstart', (e) => {
    startY = e.touches[0].pageY;
});

document.addEventListener('touchmove', (e) => {
    currentY = e.touches[0].pageY;
    pullDistance = currentY - startY;
    
    if (pullDistance > 0 && window.scrollY === 0) {
        // Simulate pull-to-refresh visual feedback
        const appContent = document.querySelector('.app-content');
        if (pullDistance > 100) {
            appContent.style.transform = `translateY(${Math.min(pullDistance * 0.5, 50)}px)`;
        }
    }
});

document.addEventListener('touchend', () => {
    const appContent = document.querySelector('.app-content');
    appContent.style.transform = 'translateY(0)';
    
    if (pullDistance > 100) {
        // Trigger refresh
        console.log('تم تحديث التطبيق');
    }
    
    pullDistance = 0;
});

console.log('تم تحميل تطبيق MINI DCA المحمول بنجاح! 📱');
