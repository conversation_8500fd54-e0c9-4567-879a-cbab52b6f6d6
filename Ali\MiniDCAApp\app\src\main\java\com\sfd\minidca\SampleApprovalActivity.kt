package com.sfd.minidca

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.cardview.widget.CardView

class SampleApprovalActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_sample_approval)
        
        setupUI()
        setupMenuItems()
    }
    
    private fun setupUI() {
        // إعداد شريط العنوان
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "اعتماد العينات"
        
        // إعداد اتجاه RTL
        window.decorView.layoutDirection = android.view.View.LAYOUT_DIRECTION_RTL
    }
    
    private fun setupMenuItems() {
        // عينات المواد
        findViewById<CardView>(R.id.cardMaterialSamples).setOnClickListener {
            animateCardClick(it as CardView) {
                startActivity(Intent(this, MaterialSamplesActivity::class.java))
            }
        }
        
        // عينات تنفيذية الأعمال
        findViewById<CardView>(R.id.cardExecutionSamples).setOnClickListener {
            animateCardClick(it as CardView) {
                startActivity(Intent(this, ExecutionSamplesActivity::class.java))
            }
        }
    }
    
    private fun animateCardClick(card: CardView, action: () -> Unit) {
        card.animate()
            .scaleX(0.95f)
            .scaleY(0.95f)
            .setDuration(100)
            .withEndAction {
                card.animate()
                    .scaleX(1.0f)
                    .scaleY(1.0f)
                    .setDuration(100)
                    .withEndAction {
                        action()
                    }
            }
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
