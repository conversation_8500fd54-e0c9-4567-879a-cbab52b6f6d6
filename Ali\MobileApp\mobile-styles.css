* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #e5e7eb 0%, #f3f4f6 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    direction: rtl;
    overflow: hidden;
}

/* Mobile Container */
.mobile-container {
    width: 375px;
    height: 812px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 40px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
    border: 8px solid #374151;
}

/* Status Bar */
.status-bar {
    height: 44px;
    background: rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.status-left .time {
    font-weight: bold;
}

.status-center .notch {
    width: 150px;
    height: 30px;
    background: #000;
    border-radius: 0 0 20px 20px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 0;
}

.status-right {
    display: flex;
    align-items: center;
    gap: 5px;
}

.status-right i {
    font-size: 12px;
}

/* App Content */
.app-content {
    height: calc(100% - 44px - 80px);
    overflow-y: auto;
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    padding: 30px 20px;
    overflow-y: auto;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.screen.active {
    opacity: 1;
    transform: translateX(0);
}

/* App Header */
.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    margin-bottom: 20px;
}

.notification-container {
    position: relative;
}

.notification-icon {
    font-size: 24px;
    color: #667eea;
    cursor: pointer;
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
}

.app-header h1 {
    font-size: 20px;
    font-weight: 700;
    color: #2c3e50;
}

.app-header .fas {
    font-size: 20px;
    color: #667eea;
}

/* Organization Card */
.org-card {
    background: white;
    border-radius: 20px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
}

.org-logo {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.logo-content {
    text-align: center;
    color: white;
}

.logo-text {
    display: block;
    font-size: 16px;
    font-weight: bold;
}

.logo-subtitle {
    font-size: 8px;
    margin-top: 2px;
}

.org-info h2 {
    font-size: 16px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.org-info h3 {
    font-size: 14px;
    font-weight: 600;
    color: #3498db;
    margin-bottom: 5px;
}

.org-info p {
    font-size: 12px;
    color: #7f8c8d;
    line-height: 1.4;
}

/* Special Banner */
.special-banner {
    background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
    color: white;
    text-align: center;
    padding: 15px;
    border-radius: 15px;
    margin-bottom: 25px;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 4px 15px rgba(255,167,38,0.3);
}

/* Main Menu */
.main-menu {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 40px;
    padding: 0 16px;
}

.menu-item {
    background: #ffffff;
    border-radius: 20px;
    padding: 20px 12px;
    text-align: center;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    position: relative;
    overflow: hidden;
}

.menu-item:active {
    transform: scale(0.97);
}

.menu-item:hover {
    box-shadow: 0 6px 24px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.menu-item .menu-icon {
    width: 56px;
    height: 56px;
    margin: 0 auto 12px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    box-shadow: 0 3px 12px rgba(99, 102, 241, 0.25);
}

.menu-item span {
    font-size: 13px;
    font-weight: 600;
    color: #1f2937;
    display: block;
    line-height: 1.2;
    margin-top: 2px;
}

/* Operations Card */
.operations-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.operations-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.operations-header h3 {
    font-size: 16px;
    font-weight: 600;
}

.operation-item {
    padding: 20px;
    background: #fafbfc;
}

.operation-item p {
    margin-bottom: 8px;
    font-size: 13px;
}

.operation-title {
    font-weight: 600;
    color: #2c3e50;
}

.operation-type {
    color: #3498db;
}

.operation-progress {
    color: #27ae60;
    font-weight: 600;
}

.operation-date {
    color: #7f8c8d;
    font-size: 12px;
}

/* Screen Header */
.screen-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    margin-bottom: 30px;
}

.back-btn {
    font-size: 24px;
    color: #667eea;
    cursor: pointer;
}

.screen-header h2 {
    font-size: 20px;
    font-weight: 700;
    color: #2c3e50;
}

/* Approval Options */
.approval-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px 0;
}

.approval-card {
    background: white;
    border-radius: 20px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.approval-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.approval-card:active {
    transform: scale(0.98);
}

.approval-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 30px;
}

.approval-card h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
    line-height: 1.3;
}

.approval-card p {
    font-size: 14px;
    color: #7f8c8d;
    line-height: 1.4;
}

/* Samples Grid */
.samples-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 20px 0;
}

.sample-card {
    background: white;
    border-radius: 20px;
    padding: 25px 15px;
    text-align: center;
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.sample-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.sample-card:active {
    transform: scale(0.95);
}

.sample-icon {
    width: 50px;
    height: 50px;
    margin: 0 auto 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.sample-card h4 {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    line-height: 1.2;
}

.sample-card p {
    font-size: 11px;
    color: #7f8c8d;
    line-height: 1.3;
}

/* Bottom Navigation */
.bottom-nav {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 80px;
    background: #2c3e50;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40px;
    border-radius: 25px 25px 0 0;
}

.nav-item {
    color: #bdc3c7;
    font-size: 24px;
    cursor: pointer;
    transition: color 0.3s ease;
    padding: 10px;
}

.nav-item.active,
.nav-item:active {
    color: white;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 20px;
    width: 90%;
    max-width: 400px;
    max-height: 80%;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.close-modal {
    font-size: 24px;
    cursor: pointer;
    font-weight: bold;
}

.modal-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    padding: 15px 0;
    border-bottom: 1px solid #ecf0f1;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item h4 {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.notification-item p {
    font-size: 14px;
    color: #7f8c8d;
    margin-bottom: 5px;
    line-height: 1.4;
}

.notification-item .time {
    font-size: 12px;
    color: #bdc3c7;
}

/* Materials Container */
.materials-container {
    display: flex;
    gap: 15px;
    height: calc(100vh - 200px);
    overflow: hidden;
}

.materials-section,
.specs-section {
    flex: 1;
    background: white;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    overflow-y: auto;
    max-height: 100%;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f8f9fa;
}

.section-title i {
    color: #667eea;
    font-size: 20px;
}

.section-title h3 {
    font-size: 16px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

/* Material Items */
.material-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    margin-bottom: 10px;
    background: #f8f9fa;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.material-item:hover,
.material-item:active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateX(-5px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.material-item:hover .material-type,
.material-item:active .material-type {
    color: rgba(255,255,255,0.8);
}

.material-content h4 {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 5px 0;
    transition: color 0.3s ease;
}

.material-type {
    font-size: 12px;
    color: #7f8c8d;
    transition: color 0.3s ease;
}

.material-item i {
    color: #bdc3c7;
    font-size: 14px;
    transition: color 0.3s ease;
}

.material-item:hover i,
.material-item:active i {
    color: white;
}

/* Spec Items */
.spec-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 15px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
}

.spec-item.highlighted {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.spec-item.highlighted .spec-detail {
    color: rgba(255,255,255,0.9);
}

.spec-item.highlighted .spec-badge {
    background: rgba(255,255,255,0.2);
    color: white;
}

.spec-item:not(.highlighted):hover,
.spec-item:not(.highlighted):active {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    transform: translateX(-3px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.spec-item:not(.highlighted):hover .spec-detail,
.spec-item:not(.highlighted):active .spec-detail {
    color: rgba(255,255,255,0.8);
}

.spec-content h4 {
    font-size: 13px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 3px 0;
    transition: color 0.3s ease;
}

.spec-item.highlighted .spec-content h4 {
    color: white;
}

.spec-detail {
    font-size: 11px;
    color: #7f8c8d;
    transition: color 0.3s ease;
}

.spec-badge {
    background: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.spec-badge.premium {
    background: rgba(255,255,255,0.2);
    color: white;
    font-weight: 700;
}

/* Scrollbar Styling */
.materials-section::-webkit-scrollbar,
.specs-section::-webkit-scrollbar {
    width: 6px;
}

.materials-section::-webkit-scrollbar-track,
.specs-section::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.materials-section::-webkit-scrollbar-thumb,
.specs-section::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
}

.materials-section::-webkit-scrollbar-thumb:hover,
.specs-section::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* Execution Samples Container */
.execution-container {
    display: flex;
    gap: 15px;
    height: calc(100vh - 200px);
    overflow: hidden;
}

.execution-section,
.structural-section {
    flex: 1;
    background: white;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    overflow-y: auto;
    max-height: 100%;
}

/* Execution Items */
.execution-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    margin-bottom: 10px;
    background: #f8f9fa;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
}

.execution-item.highlighted {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: #2c3e50;
    box-shadow: 0 4px 15px rgba(255, 154, 158, 0.3);
    border: 2px solid rgba(255, 154, 158, 0.5);
}

.execution-item.empty {
    background: #ecf0f1;
    opacity: 0.6;
    cursor: not-allowed;
}

.execution-item:not(.highlighted):not(.empty):hover,
.execution-item:not(.highlighted):not(.empty):active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateX(-5px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.execution-item:not(.highlighted):not(.empty):hover .execution-type,
.execution-item:not(.highlighted):not(.empty):active .execution-type {
    color: rgba(255,255,255,0.8);
}

.execution-content h4 {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 5px 0;
    transition: color 0.3s ease;
}

.execution-type {
    font-size: 12px;
    color: #7f8c8d;
    transition: color 0.3s ease;
}

.execution-item i {
    color: #bdc3c7;
    font-size: 14px;
    transition: color 0.3s ease;
}

.execution-item:not(.empty):hover i,
.execution-item:not(.empty):active i {
    color: white;
}

.execution-item.highlighted i {
    color: #2c3e50;
}

/* Structural Items */
.structural-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 15px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
}

.structural-item.highlighted {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #2c3e50;
    box-shadow: 0 4px 15px rgba(168, 237, 234, 0.3);
}

.structural-item.highlighted .structural-detail {
    color: #2c3e50;
    opacity: 0.8;
}

.structural-item.highlighted .structural-badge {
    background: rgba(44, 62, 80, 0.1);
    color: #2c3e50;
}

.structural-item:not(.highlighted):hover,
.structural-item:not(.highlighted):active {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    transform: translateX(-3px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.structural-item:not(.highlighted):hover .structural-detail,
.structural-item:not(.highlighted):active .structural-detail {
    color: rgba(255,255,255,0.8);
}

.structural-content h4 {
    font-size: 13px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 3px 0;
    transition: color 0.3s ease;
}

.structural-detail {
    font-size: 11px;
    color: #7f8c8d;
    transition: color 0.3s ease;
}

.structural-badge {
    background: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.structural-badge.premium {
    background: rgba(44, 62, 80, 0.1);
    color: #2c3e50;
    font-weight: 700;
}

/* Scrollbar Styling for Execution */
.execution-section::-webkit-scrollbar,
.structural-section::-webkit-scrollbar {
    width: 6px;
}

.execution-section::-webkit-scrollbar-track,
.structural-section::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.execution-section::-webkit-scrollbar-thumb,
.structural-section::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border-radius: 10px;
}

.execution-section::-webkit-scrollbar-thumb:hover,
.structural-section::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

/* Mobile Responsive */
@media (max-width: 400px) {
    .mobile-container {
        width: 100vw;
        height: 100vh;
        border-radius: 0;
        border: none;
    }

    .materials-container,
    .execution-container {
        flex-direction: column;
        height: auto;
    }

    .materials-section,
    .specs-section,
    .execution-section,
    .structural-section {
        max-height: 300px;
    }

    .section-title h3 {
        font-size: 14px;
    }

    .material-item,
    .spec-item,
    .execution-item,
    .structural-item {
        padding: 12px;
    }

    .material-content h4,
    .spec-content h4,
    .execution-content h4,
    .structural-content h4 {
        font-size: 13px;
    }
}

/* Receive Work Screen Styles */
.work-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    padding: 20px;
    height: calc(100vh - 120px);
    overflow-y: auto;
}

.construction-section,
.finishing-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.construction-section .section-title {
    background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
}

.finishing-section .section-title {
    background: linear-gradient(135deg, #e17055 0%, #fdcb6e 100%);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 25px;
    padding: 15px 20px;
    border-radius: 15px;
    color: white;
    box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);
}

.section-title i {
    font-size: 24px;
    color: #ffffff;
}

.section-title h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Work Items */
.work-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 22px;
    margin-bottom: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 15px;
    border: 2px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.work-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.work-item:hover::before {
    left: 100%;
}

.work-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
    border-color: #6c5ce7;
}

.work-item.highlighted {
    background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
    color: white;
    border-color: #5a4fcf;
    box-shadow: 0 8px 25px rgba(108, 92, 231, 0.3);
}

.work-item.highlighted:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(108, 92, 231, 0.4);
}

.work-content {
    flex: 1;
    text-align: right;
}

.work-content h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: 700;
    color: #2d3436;
}

.work-item.highlighted .work-content h4 {
    color: white;
}

.work-type {
    font-size: 13px;
    color: #636e72;
    font-weight: 500;
}

.work-item.highlighted .work-type {
    color: rgba(255, 255, 255, 0.9);
}

.work-status {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    min-width: 80px;
}

.work-status.completed {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
    color: white;
}

.work-status.pending {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    color: white;
}

.work-status.review {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
}

/* Finish Items */
.finish-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 22px;
    margin-bottom: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 15px;
    border: 2px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.finish-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.finish-item:hover::before {
    left: 100%;
}

.finish-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
    border-color: #e17055;
}

.finish-item.highlighted {
    background: linear-gradient(135deg, #e17055 0%, #fdcb6e 100%);
    color: white;
    border-color: #d63031;
    box-shadow: 0 8px 25px rgba(225, 112, 85, 0.3);
}

.finish-item.highlighted:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(225, 112, 85, 0.4);
}

.finish-content {
    flex: 1;
    text-align: right;
}

.finish-content h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: 700;
    color: #2d3436;
}

.finish-item.highlighted .finish-content h4 {
    color: white;
}

.finish-detail {
    font-size: 13px;
    color: #636e72;
    font-weight: 500;
}

.finish-item.highlighted .finish-detail {
    color: rgba(255, 255, 255, 0.9);
}

.finish-badge {
    padding: 6px 14px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: linear-gradient(135deg, #ddd6fe 0%, #c7d2fe 100%);
    color: #5b21b6;
    border: 1px solid #c7d2fe;
}

.finish-badge.premium {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    border: 1px solid #fde68a;
}

/* Permissions Screen Styles */
.permissions-container {
    padding: 20px;
    height: calc(100vh - 200px);
    overflow-y: auto;
}

.permissions-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 20px;
}

.permissions-section .section-title {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
    box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
}

/* Permission Items */
.permission-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 25px;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 18px;
    border: 2px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.permission-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.permission-item:hover::before {
    left: 100%;
}

.permission-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: #00b894;
}

.permission-item.highlighted {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
    color: white;
    border-color: #00a085;
    box-shadow: 0 10px 30px rgba(0, 184, 148, 0.3);
}

.permission-item.highlighted:hover {
    transform: translateY(-6px);
    box-shadow: 0 20px 50px rgba(0, 184, 148, 0.4);
}

.permission-content {
    flex: 1;
    text-align: right;
}

.permission-content h4 {
    margin: 0 0 8px 0;
    font-size: 17px;
    font-weight: 700;
    color: #2d3436;
    line-height: 1.3;
}

.permission-item.highlighted .permission-content h4 {
    color: white;
}

.permission-detail {
    font-size: 14px;
    color: #636e72;
    font-weight: 500;
}

.permission-item.highlighted .permission-detail {
    color: rgba(255, 255, 255, 0.9);
}

.permission-status {
    padding: 10px 18px;
    border-radius: 25px;
    font-size: 13px;
    font-weight: 700;
    text-align: center;
    min-width: 90px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.permission-status.active {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
}

.permission-status.pending {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(253, 203, 110, 0.3);
}

.permission-status.review {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
}

/* Permission Actions */
.permission-actions {
    display: flex;
    justify-content: space-around;
    padding: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px 20px 0 0;
    box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    bottom: 80px;
    left: 8px;
    right: 8px;
    margin: 0 auto;
    max-width: 359px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px 20px;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    min-width: 80px;
}

.action-btn i {
    font-size: 20px;
}

.action-btn span {
    font-size: 11px;
    text-align: center;
}

.action-btn.primary {
    background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);
}

.action-btn.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(108, 92, 231, 0.4);
}

.action-btn.secondary {
    background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(99, 110, 114, 0.3);
}

.action-btn.secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(99, 110, 114, 0.4);
}

.action-btn.warning {
    background: linear-gradient(135deg, #e17055 0%, #fdcb6e 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(225, 112, 85, 0.3);
}

.action-btn.warning:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(225, 112, 85, 0.4);
}
