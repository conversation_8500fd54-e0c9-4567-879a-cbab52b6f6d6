* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    direction: rtl;
    color: #333;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.notification-icon {
    font-size: 24px;
    position: relative;
    cursor: pointer;
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.main-title {
    font-size: 20px;
    font-weight: 600;
}

.back-arrow {
    font-size: 24px;
    cursor: pointer;
}

/* Organization Info */
.org-info {
    background: white;
    padding: 20px;
    margin: 20px;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 20px;
}

.logo-container {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    width: 80px;
    height: 80px;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
}

.logo-text {
    font-size: 18px;
    font-weight: bold;
}

.logo-subtitle {
    font-size: 8px;
    text-align: center;
    margin-top: 2px;
}

.org-details {
    flex: 1;
}

.org-name {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.dept-name {
    font-size: 16px;
    font-weight: 600;
    color: #3498db;
    margin-bottom: 5px;
}

.dept-desc {
    font-size: 14px;
    color: #7f8c8d;
    line-height: 1.4;
}

/* Special Banner */
.special-banner {
    background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
    color: white;
    text-align: center;
    padding: 15px;
    margin: 0 20px;
    border-radius: 10px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(255,167,38,0.3);
}

/* Main Menu */
.main-menu {
    padding: 20px;
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    max-width: 1200px;
    margin: 0 auto;
}

.menu-item {
    background: white;
    border-radius: 15px;
    padding: 25px 15px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.menu-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #3498db;
}

.menu-icon {
    width: 50px;
    height: 50px;
    margin: 0 auto 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.menu-text {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    display: block;
}

/* Latest Operations */
.latest-operations {
    margin: 20px;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.section-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.section-header h3 {
    font-size: 16px;
    font-weight: 600;
}

.section-icon {
    font-size: 18px;
}

.operations-list {
    padding: 0;
}

.operation-item {
    padding: 20px;
    border-bottom: 1px solid #ecf0f1;
    background: #fafbfc;
}

.operation-item:last-child {
    border-bottom: none;
}

.operation-content p {
    margin-bottom: 8px;
    font-size: 14px;
}

.operation-title {
    font-weight: 600;
    color: #2c3e50;
}

.operation-type {
    color: #3498db;
}

.operation-progress {
    color: #27ae60;
    font-weight: 600;
}

.operation-date {
    color: #7f8c8d;
    font-size: 13px;
}

/* Bottom Navigation */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #2c3e50;
    display: flex;
    justify-content: center;
    padding: 15px 0;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
}

.nav-item {
    color: #bdc3c7;
    font-size: 24px;
    margin: 0 30px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.nav-item.active,
.nav-item:hover {
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .menu-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
    
    .org-info {
        margin: 15px;
        padding: 15px;
    }
    
    .special-banner {
        margin: 0 15px;
        font-size: 14px;
    }
    
    .latest-operations {
        margin: 15px;
    }
}

@media (max-width: 480px) {
    .menu-grid {
        grid-template-columns: 1fr;
    }
    
    .header-content {
        padding: 0 10px;
    }
    
    .main-title {
        font-size: 18px;
    }
}
